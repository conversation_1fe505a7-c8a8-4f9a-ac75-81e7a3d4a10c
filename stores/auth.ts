import { defineS<PERSON> } from 'pinia'
import { AuthAPI } from '~/lib/api/auth'
import type { User } from '~/types/user'

interface AuthState {
    user: User | null
    isAuthenticated: boolean
    loading: boolean
    errors: Record<string, string[]> | null
    token: string | null
}

interface RegisterData {
    username: string
    email: string
    password: string
}

export const useAuthStore = defineStore('auth', {
    state: (): AuthState => ({
        user: null,
        isAuthenticated: false,
        loading: false,
        errors: null,
        token: null
    }),
    persist: true,

    getters: {
        currentUser: (state): User | null => state.user,
        isLoading: (state): boolean => state.loading,
        hasErrors: (state): boolean => state.errors !== null,
        isUserAuthenticated: (state): boolean => state.isAuthenticated
    },

    actions: {
        resetState() {
            this.user = null
            this.isAuthenticated = false
            this.loading = false
            this.errors = null
            this.token = null
        },

        initializeAuth() {
            const token = localStorage.getItem('token')
            if (token) {
                this.isAuthenticated = true
                this.token = token
                // TODO: 토큰으로 사용자 정보 가져오기
            }
        },

        async register(credentials: { username: string; email: string; password: string }) {
            this.loading = true
            this.errors = null

            try {
                const response = await AuthAPI.register({
                    user: {
                        username: credentials.username,
                        email: credentials.email,
                        password: credentials.password
                    }
                })

                this.user = response.user
                this.isAuthenticated = true

                const token = response.user.token
                localStorage.setItem('token', token)

                return response.user
            } catch (error) {
                if (error instanceof Error) {
                    try {
                        this.errors = JSON.parse(error.message)
                    } catch {
                        this.errors = {
                            'general': [error.message]
                        }
                    }
                }
                throw error
            } finally {
                this.loading = false
            }
        },

        logout() {
            this.resetState()
            localStorage.removeItem('token')
        },

        async login(credentials: { email: string; password: string }) {
            this.loading = true
            this.errors = null

            try {
                const response = await AuthAPI.login({
                    user: {
                        email: credentials.email,
                        password: credentials.password
                    }
                })

                this.user = response.user
                this.isAuthenticated = true

                const token = response.user.token
                localStorage.setItem('token', token)

                return response.user
            } catch (error) {
                if (error instanceof Error) {
                    try {
                        this.errors = JSON.parse(error.message)
                    } catch {
                        this.errors = {
                            'general': ['이메일 또는 비밀번호가 올바르지 않습니다.']
                        }
                    }
                }
                throw error
            } finally {
                this.loading = false
            }
        }
    }
}) 