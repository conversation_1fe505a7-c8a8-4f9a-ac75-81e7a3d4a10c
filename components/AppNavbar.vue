<template>
    <nav class="navbar navbar-light">
        <div class="container">
            <NuxtLink class="navbar-brand" to="/">conduit</NuxtLink>

            <ul class="nav navbar-nav pull-xs-right">
                <li class="nav-item">
                    <NuxtLink class="nav-link" to="/" active-class="active">홈</NuxtLink>
                </li>

                <!-- 로그인한 경우 -->
                <template v-if="authStore.isAuthenticated">
                    <li class="nav-item">
                        <NuxtLink class="nav-link" to="#">
                            <i class="ion-compose"></i>&nbsp;글쓰기
                        </NuxtLink>
                    </li>
                    <li class="nav-item">
                        <NuxtLink class="nav-link" to="#">
                            <i class="ion-gear-a"></i>&nbsp;설정
                        </NuxtLink>
                    </li>
                    <li class="nav-item">
                        <NuxtLink class="nav-link" :to="`/@${authStore.user?.username}`">
                            <img v-if="authStore.user?.image" :src="authStore.user.image" class="user-pic" alt="프로필">
                            {{ authStore.user?.username }}
                        </NuxtLink>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" @click.prevent="handleLogout">로그아웃</a>
                    </li>
                </template>

                <!-- 로그인하지 않은 경우 -->
                <template v-else>
                    <li class="nav-item">
                        <NuxtLink class="nav-link" to="/login">로그인</NuxtLink>
                    </li>
                    <li class="nav-item">
                        <NuxtLink class="nav-link" to="/register">회원가입</NuxtLink>
                    </li>
                </template>
            </ul>
        </div>
    </nav>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const handleLogout = () => {
    authStore.logout()
    router.push('/login')
}
</script>

<style scoped>
.user-pic {
    height: 26px;
    border-radius: 50%;
    margin-right: 5px;
}
</style>