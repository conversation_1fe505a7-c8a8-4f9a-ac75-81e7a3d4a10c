<template>
  <div class="auth-page">
    <div class="container page">
      <div class="row">
        <div class="col-md-6 offset-md-3 col-xs-12">
          <h1 class="text-xs-center">로그인</h1>
          <p class="text-xs-center">
            <NuxtLink to="/register">계정이 필요하신가요?</NuxtLink>
          </p>

          <!-- 에러 메시지 표시 -->
          <ul v-if="authStore.errors" class="error-messages">
            <template v-for="(errors, field) in authStore.errors" :key="field">
              <li v-for="error in errors" :key="error">
                {{ field === 'general' ? error : `${field} ${error}` }}
              </li>
            </template>
          </ul>

          <form @submit.prevent="handleSubmit">
            <fieldset :disabled="authStore.loading">
              <fieldset class="form-group">
                <input v-model="email" class="form-control form-control-lg" type="email" placeholder="이메일" required />
              </fieldset>
              <fieldset class="form-group">
                <input v-model="password" class="form-control form-control-lg" type="password" placeholder="비밀번호"
                  required />
              </fieldset>
              <button class="btn btn-lg btn-primary pull-xs-right" type="submit" :disabled="authStore.loading">
                {{ authStore.loading ? '로그인 중...' : '로그인' }}
              </button>
            </fieldset>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '~/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')

async function handleSubmit() {
  try {
    await authStore.login({
      email: email.value,
      password: password.value
    })
    router.push('/')
  } catch (error) {
    console.error('로그인 실패:', error)
  }
}
</script>

<style scoped>
.error-messages {
  color: #b85c5c;
  font-weight: bold;
  list-style-type: none;
  padding: 0;
  margin-bottom: 20px;
}
</style>