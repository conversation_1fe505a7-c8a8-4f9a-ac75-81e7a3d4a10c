{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --port 3001", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"nuxt": "^3.16.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@nuxt/content": "^3.4.0", "@nuxt/fonts": "^0.11.1", "@nuxt/icon": "^1.12.0", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.5", "@nuxt/test-utils": "^3.17.2", "@nuxt/ui": "^3.0.2", "@pinia/nuxt": "^0.11.0", "@unhead/vue": "^2.0.5", "pinia-plugin-persistedstate": "^4.2.0", "typescript": "^5.8.3"}}