const API_URL = 'http://localhost:3000/api'

export interface RegisterRequest {
    user: {
        username: string
        email: string
        password: string
    }
}

export interface AuthResponse {
    user: {
        email: string
        token: string
        username: string
        bio: string
        image: string | null
    }
}

export interface LoginRequest {
    user: {
        email: string
        password: string
    }
}

export class AuthAPI {
    static async register(data: RegisterRequest): Promise<AuthResponse> {
        const response = await fetch(`${API_URL}/users`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })

        if (!response.ok) {
            const errorData = await response.json()
            throw new Error(JSON.stringify(errorData.errors))
        }

        return response.json()
    }

    static async login(data: LoginRequest): Promise<AuthResponse> {
        const response = await fetch(`${API_URL}/users/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })

        if (!response.ok) {
            const errorData = await response.json()
            throw new Error(JSON.stringify(errorData.errors))
        }

        return response.json()
    }
} 